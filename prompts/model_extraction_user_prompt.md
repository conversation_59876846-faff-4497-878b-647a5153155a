Please refer to the below pre-clinical Antibody-Drug Conjugate(ADC) study and extract ALL the experimental models used to study the ADC provided. 
You must list down every experimental model that this ADC is evaluated in.

Specific Antibody-Drug Conjugate(ADC) to focus: {{ADC}}

Please follow the below steps:

Step-1:
YOU HAVE TO LOAD ALL MODEL NAMES INTO MEMORY!!

Please extract names of ALL experimental models used to study the ADC provided above, (take special care in ensuring that every single experimental model is picked) and save them into memory along with the step-by-step thought process quoting text snippets from the research paper supporting your arguments for being exhaustive in your extraction. Even extract the models that are used for safety and pharmacokinetics studies, donot stay limited to cancer related studies only.

CRITICAL REQUIREMENT:  Make sure that you pick EVERY SINGLE experimental model that this ADC is evaluated in.

After this please do the following:

Step-2:
For the ADC provided, Extract all experimental models used with supporting evidence as citations. 

Instructions:

For each experimental model that the specified ADC (Antibody Drug Conjugate) is studied in, extract the following:

1. citations:
   - List of sentences or fragments from the research paper that support the extraction of the below attributes for this model.
   - Each citation sentence should provide clear evidence for one or more of the below attributes.
   - Include enough context (typically 20-30 words long per sentence) to validate the extraction for every field in this model.
   - Ensure the citation includes relevant descriptors that support the classification.
   - The citations must contain the name of the ADC and the experimental model on which this ADC has been evaluated. This must provide sufficient context to link this experimental model to the ADC.
   - The citations must be EXACT exerpts from the research paper, you can add many exerpts but cant modify any!

2. model_name:
   - The exact, detailed designation of the experimental biological system as stated in the original source or publication, including all specific identifiers such as species, strain, cell type, disease state, genetic modification, engraftment status, and any other relevant distinguishing features that uniquely identify the model without abstraction or generalization.

3. cancer_type:
   - A cancer type refers to the broad, primary classification of cancer, based on the organ or tissue where the cancer originates. It is essentially the location in the body where the cancerous cells began to form.

4. cancer_subtype (if specified):
   - A cancer subtype is a further classification within a cancer type, based on specific histological, genetic, or molecular characteristics of the tumor cells. Subtypes can influence the cancer's behavior, its response to treatment, and its prognosis.

Rules:
- Do not duplicate the same model; each model should appear only once in your output, even if mentioned multiple times. For this: Take special care in picking a model only once even if its constituent is mentioned along with the model, only the model will be picked and not the constituent.
- For each model, include all relevant supporting citations in the citations list.
- Do not miss any models.

CRITICAL REQUIREMENT: In this step, Only extract experimental models that our ADC is evaluated in. And extract ALL such models, do not stay limited to cancer related models only. Donot stay limited to any kind of model or study, extract all models mentioned in relation to the ADC provided.

To help you, we have provided a rough extraction of all experimental models that are mentioned in this text here (These are not necessarally seperated into a list of unique models yet but the below text contains many models that you MUST consider):
{{ALL_MODELS}}


Text to analyze:
{{TEXT}}


Return all models used with this ADC, ensuring each model appears only once in your response and every unique model must be mentioned distinctly as a different model in your response and ensure you pick ALL the models related to this ADC as per the text provided. Donot club multiple models together, each unique model with its details details must be returned as a separate model in your response. 

CRITICAL REQUIREMENT: - Donot club seperate models together (even if thats the case in rough model extraction provided). Make sure the model name of every model you give is not a comma seperated list of multiple model names! Each unique model gets a new experimental model object, and model name for every object contains a model identifier only! This is either cell line name or tissue specimen name or the organism name only in most cases.
- Donot add model type (Cell Line, CDX, PDX, Xenograft etc) in the model name parameter! Even if its present in rough model extraction provided! Just trim them to contain minimal model name in that case.
- Extract ALL models together as a SINGLE list of a experimental model objects! Never extract multiple lists, have all models in a single list only.
